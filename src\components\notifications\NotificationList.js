import React, { useState, useEffect } from 'react';
import './NotificationList.css';

/**
 * NotificationList Component
 * Displays a list of notifications with actions
 * Follows the existing cyber theme styling
 */
const NotificationList = ({ userId, onNotificationClick }) => {
    const [notifications, setNotifications] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        if (!userId) {
            setNotifications([]);
            setIsLoading(false);
            return;
        }

        // Initial load of notifications
        const loadNotifications = async () => {
            try {
                setIsLoading(true);
                if (window.notificationService) {
                    const userNotifications = await window.notificationService.getUserNotifications(userId, 50);
                    setNotifications(userNotifications);
                    setError(null);
                } else {
                    setNotifications([]);
                    setError('Notification service not available');
                }
            } catch (err) {
                console.error('Error loading notifications:', err);
                setError('Failed to load notifications');
                setNotifications([]);
            } finally {
                setIsLoading(false);
            }
        };

        loadNotifications();

        // Poll for updates every 30 seconds
        const interval = setInterval(loadNotifications, 30000);

        const unsubscribe = () => {
            clearInterval(interval);
        };

        return () => {
            if (unsubscribe) {
                unsubscribe();
            }
        };
    }, [userId]);

    const handleNotificationClick = async (notification) => {
        try {
            // Mark as read if not already read
            if (!notification.isRead && window.notificationService) {
                await window.notificationService.markNotificationAsRead(notification.id);
                // Update local state
                setNotifications(prev =>
                    prev.map(n => n.id === notification.id ? {...n, isRead: true} : n)
                );
            }

            // Call the parent callback if provided
            if (onNotificationClick) {
                onNotificationClick(notification);
            }
        } catch (error) {
            console.error('Error handling notification click:', error);
        }
    };

    const handleMarkAllAsRead = async () => {
        try {
            if (window.notificationService) {
                const unreadNotifications = notifications.filter(n => !n.isRead);
                for (const notification of unreadNotifications) {
                    await window.notificationService.markNotificationAsRead(notification.id);
                }
                // Update local state
                setNotifications(prev =>
                    prev.map(n => ({...n, isRead: true}))
                );
            }
        } catch (error) {
            console.error('Error marking all notifications as read:', error);
        }
    };

    const formatTimeAgo = (date) => {
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);
        
        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
        if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
        
        return date.toLocaleDateString();
    };

    const getNotificationIcon = (type) => {
        switch (type) {
            case 'tournament_registration_closed':
                return '🔒';
            case 'tournament_live':
                return '🚀';
            case 'match_dispute':
                return '⚠️';
            case 'upcoming_match':
                return '⚔️';
            default:
                return '📢';
        }
    };

    if (isLoading) {
        return (
            <div className="notification-list">
                <div className="notification-header">
                    <h3>Notifications</h3>
                </div>
                <div className="notification-loading">
                    <div className="loading-spinner"></div>
                    <p>Loading notifications...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="notification-list">
                <div className="notification-header">
                    <h3>Notifications</h3>
                </div>
                <div className="notification-error">
                    <p>{error}</p>
                    <button 
                        className="btn btn-cyber-secondary"
                        onClick={() => window.location.reload()}
                    >
                        Retry
                    </button>
                </div>
            </div>
        );
    }

    const unreadCount = notifications.filter(n => !n.isRead).length;

    return (
        <div className="notification-list">
            <div className="notification-header">
                <h3>Notifications</h3>
                {unreadCount > 0 && (
                    <button 
                        className="btn btn-cyber-secondary mark-all-read-btn"
                        onClick={handleMarkAllAsRead}
                        title="Mark all as read"
                    >
                        Mark All Read
                    </button>
                )}
            </div>

            {notifications.length === 0 ? (
                <div className="notification-empty">
                    <div className="empty-icon">🔔</div>
                    <p>No notifications yet</p>
                    <span className="empty-subtitle">You'll see tournament updates and match alerts here</span>
                </div>
            ) : (
                <div className="notification-items">
                    {notifications.map(notification => (
                        <div
                            key={notification.id}
                            className={`notification-item ${!notification.isRead ? 'unread' : 'read'}`}
                            onClick={() => handleNotificationClick(notification)}
                        >
                            <div className="notification-icon">
                                {getNotificationIcon(notification.type)}
                            </div>
                            <div className="notification-content">
                                <div className="notification-title">
                                    {notification.title}
                                    {!notification.isRead && <span className="unread-dot"></span>}
                                </div>
                                <div className="notification-message">
                                    {notification.message}
                                </div>
                                <div className="notification-time">
                                    {formatTimeAgo(notification.createdAt)}
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default NotificationList;
